// Re-export step types from organized modules
export type {
  RpaStep,
  // Navigation
  NavigateStep, GoBackStep, GoForwardStep, ReloadStep,
  // Interaction
  ClickStep, FillStep, TypeStep, SelectOptionStep, CheckStep, UncheckStep, ConditionalClickStep,
  // Waiting
  WaitForSelectorStep, WaitForTimeoutStep, WaitForUrlStep,
  // Extraction
  ExtractTextStep, ExtractAttributeStep, TakeScreenshotStep,
  // Credentials
  FillPasswordStep, Fill2FAStep,
  // Files
  DownloadFileStep,
  // AI
  ExtractPdfValuesStep,
  // API
  FortnoxCreateVoucherStep, FortnoxUploadFileStep, FortnoxAttachFileToVoucherStep, FortnoxUploadAndCreateVoucherStep
} from './types/steps';
import type {
  RpaStep,
  // Navigation
  NavigateStep, GoBackStep, GoForwardStep, ReloadStep,
  // Interaction
  ClickStep, FillStep, TypeStep, SelectOptionStep, CheckStep, UncheckStep, ConditionalClickStep,
  // Waiting
  WaitForSelectorStep, WaitForTimeoutStep, WaitForUrlStep,
  // Extraction
  ExtractTextStep, ExtractAttributeStep, TakeScreenshotStep,
  // Credentials
  FillPasswordStep, Fill2FAStep,
  // Files
  DownloadFileStep,
  // AI
  ExtractPdfValuesStep,
  // API
  FortnoxCreateVoucherStep, FortnoxUploadFileStep, FortnoxAttachFileToVoucherStep, FortnoxUploadAndCreateVoucherStep
} from './types/steps';

// Flow definition
export interface RpaFlow {
  id: string;
  name: string;
  description?: string;
  customerId: string;
  steps: RpaStep[];
  variables?: Record<string, any>;
  settings?: FlowSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface FlowSettings {
  browser?: 'chromium' | 'firefox' | 'webkit';
  headless?: boolean;
  viewport?: {
    width: number;
    height: number;
  };
  userAgent?: string;
  locale?: string;
  timezone?: string;
}

// Credential types
export interface CredentialBase {
  id: string;
  name: string;
  description?: string;
  type: 'password' | '2fa';
  createdAt: Date;
  updatedAt: Date;
}

export interface PasswordCredential extends CredentialBase {
  type: 'password';
  username: string;
  // password is encrypted and stored separately
}

export interface TwoFactorCredential extends CredentialBase {
  type: '2fa';
  // secret is encrypted and stored separately
}

export type Credential = PasswordCredential | TwoFactorCredential;

// API request/response types for credentials
export interface CreatePasswordCredentialRequest {
  name: string;
  description?: string;
  username: string;
  password: string;
}

export interface CreateTwoFactorCredentialRequest {
  name: string;
  description?: string;
  secret: string;
}

export type CreateCredentialRequest = CreatePasswordCredentialRequest | CreateTwoFactorCredentialRequest;

export interface UpdatePasswordCredentialRequest {
  name?: string;
  description?: string;
  username?: string;
  password?: string;
}

export interface UpdateTwoFactorCredentialRequest {
  name?: string;
  description?: string;
  secret?: string;
}

// Customer types
export interface Customer {
  id: string;
  customerNumber: string;
  name: string;
  vismaNumber?: string;
  createdAt: Date;
  updatedAt: Date;
}

// OAuth2 Provider types
export type OAuth2Provider = 'eEkonomi' | 'Fortnox' | 'manual';

export interface OAuth2Config {
  clientId: string;
  clientSecret: string;
  authUrl: string;
  tokenUrl: string;
  scopes: string[];
  redirectUri: string;
}

// Customer token types
export interface CustomerToken {
  id: string;
  customerId: string;
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  hasApiToken?: boolean;
  hasRefreshToken?: boolean;
  expiresAt?: Date;
  isExpired?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Customer request types
export interface CreateCustomerRequest {
  customerNumber: string;
  name: string;
  vismaNumber?: string;
}

export interface UpdateCustomerRequest {
  customerNumber?: string;
  name?: string;
  vismaNumber?: string;
}

// Customer token request types
export interface CreateCustomerTokenRequest {
  name: string;
  description?: string;
  provider: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

export interface UpdateCustomerTokenRequest {
  name?: string;
  description?: string;
  provider?: OAuth2Provider;
  apiToken?: string;
  refreshToken?: string;
  expiresAt?: Date;
}

// OAuth2 specific request types
export interface InitiateOAuth2Request {
  customerId: string;
  provider: OAuth2Provider;
  tokenName: string;
  description?: string;
}

export interface OAuth2CallbackRequest {
  code: string;
  state: string;
}

export interface OAuth2TokenResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in: number;
}

export type UpdateCredentialRequest = UpdatePasswordCredentialRequest | UpdateTwoFactorCredentialRequest;

// Execution types
export interface FlowExecution {
  id: string;
  flowId: string;
  status: ExecutionStatus;
  startedAt: Date;
  completedAt?: Date;
  error?: string;
  logs: ExecutionLog[];
  results?: Record<string, any>;
}

export type ExecutionStatus = 
  | 'pending'
  | 'running'
  | 'completed'
  | 'failed'
  | 'cancelled';

export interface ExecutionLog {
  timestamp: Date;
  level: 'info' | 'warn' | 'error' | 'debug';
  message: string;
  stepId?: string;
  data?: any;
}

// API types
export interface CreateFlowRequest {
  name: string;
  description?: string;
  customerId: string;
  steps: RpaStep[];
  settings?: FlowSettings;
}

export interface UpdateFlowRequest {
  name?: string;
  description?: string;
  customerId?: string;
  steps?: RpaStep[];
  settings?: FlowSettings;
}

export interface ExecuteFlowRequest {
  flowId: string;
  variables?: Record<string, any>;
}

// Schedule types
export interface FlowSchedule {
  id: string;
  flowId: string;
  name: string;
  description?: string;
  cronExpression: string;
  timezone: string;
  enabled: boolean;
  variables?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
  lastRunAt?: Date;
  nextRunAt?: Date;
}

export interface CreateScheduleRequest {
  flowId: string;
  name: string;
  description?: string;
  cronExpression: string;
  timezone?: string;
  enabled?: boolean;
  variables?: Record<string, any>;
}

export interface UpdateScheduleRequest {
  name?: string;
  description?: string;
  cronExpression?: string;
  timezone?: string;
  enabled?: boolean;
  variables?: Record<string, any>;
}

export interface ScheduleQuery {
  flowId?: string;
  enabled?: boolean;
  limit?: number;
  offset?: number;
}

// Cron expression validation and parsing
export interface CronExpressionInfo {
  expression: string;
  description: string;
  nextRuns: Date[];
  isValid: boolean;
  error?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// React Flow types for frontend
export interface FlowNode {
  id: string;
  type: string;
  position: { x: number; y: number };
  data: {
    step: RpaStep;
    label: string;
  };
}

export interface FlowEdge {
  id: string;
  source: string;
  target: string;
  type?: string;
}

export interface FlowData {
  nodes: FlowNode[];
  edges: FlowEdge[];
}

// Validation types
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

// Utility function for generating IDs
export function generateId(): string {
  return Math.random().toString(36).substring(2, 11) + Date.now().toString(36)
}

/**
 * Generate default variable name based on step type
 */
export function getDefaultVariableName(stepType: string, stepIndex?: number): string {
  const suffix = stepIndex !== undefined ? `_${stepIndex + 1}` : '';

  switch (stepType) {
    case 'extractText':
      return `extractedText${suffix}`;
    case 'extractAttribute':
      return `extractedAttribute${suffix}`;
    case 'takeScreenshot':
      return `screenshot${suffix}`;
    case 'downloadFile':
      return `downloadedFile${suffix}`;
    case 'extractPdfValues':
      return `extractedData${suffix}`;
    // Fortnox API steps
    case 'fortnoxCreateVoucher':
      return `fortnoxVoucher${suffix}`;
    case 'fortnoxUploadFile':
      return `fortnoxFile${suffix}`;
    case 'fortnoxAttachFileToVoucher':
      return `fortnoxAttachment${suffix}`;
    case 'fortnoxUploadAndCreateVoucher':
      return `fortnoxVoucherWithFile${suffix}`;
    // AI steps
    case 'processWithLLM':
      return `aiProcessed${suffix}`;
    default:
      return `variable${suffix}`;
  }
}

// Utility functions for schedules
export function createEmptySchedule(flowId: string, name: string): FlowSchedule {
  return {
    id: generateId(),
    flowId,
    name,
    description: '',
    cronExpression: '0 9 * * 1-5', // Default: 9 AM on weekdays
    timezone: 'UTC',
    enabled: true,
    variables: {},
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

// Common cron expressions
export const COMMON_CRON_EXPRESSIONS = {
  'Every minute': '* * * * *',
  'Every 5 minutes': '*/5 * * * *',
  'Every 15 minutes': '*/15 * * * *',
  'Every 30 minutes': '*/30 * * * *',
  'Every hour': '0 * * * *',
  'Every 2 hours': '0 */2 * * *',
  'Every 6 hours': '0 */6 * * *',
  'Every 12 hours': '0 */12 * * *',
  'Daily at 9 AM': '0 9 * * *',
  'Daily at 6 PM': '0 18 * * *',
  'Weekdays at 9 AM': '0 9 * * 1-5',
  'Weekends at 10 AM': '0 10 * * 6,0',
  'Weekly on Monday at 9 AM': '0 9 * * 1',
  'Monthly on 1st at 9 AM': '0 9 1 * *',
  'Yearly on Jan 1st at 9 AM': '0 9 1 1 *'
} as const

export function getCronDescription(cronExpression: string): string {
  // Find matching common expression
  for (const [description, expression] of Object.entries(COMMON_CRON_EXPRESSIONS)) {
    if (expression === cronExpression) {
      return description
    }
  }

  // Return the expression itself if no match found
  return cronExpression
}

// Utility functions for creating steps
export function createEmptyFlow(name: string, customerId: string = ''): RpaFlow {
  return {
    id: generateId(),
    name,
    description: '',
    customerId,
    steps: [],
    createdAt: new Date(),
    updatedAt: new Date()
  }
}

export function createStepFromType(stepType: string): RpaStep {
  const baseStep = {
    id: generateId(),
    type: stepType as any,
    name: '',
    timeout: 30000
  }

  switch (stepType) {
    case 'navigate':
      return { ...baseStep, type: 'navigate', url: 'https://example.com', waitUntil: 'load' }
    case 'goBack':
      return { ...baseStep, type: 'goBack' }
    case 'goForward':
      return { ...baseStep, type: 'goForward' }
    case 'reload':
      return { ...baseStep, type: 'reload' }
    case 'click':
      return { ...baseStep, type: 'click', selector: 'button', button: 'left' }
    case 'fill':
      return { ...baseStep, type: 'fill', selector: 'input', value: 'placeholder text' }
    case 'type':
      return { ...baseStep, type: 'type', selector: 'input', text: 'text to type', delay: 0 }
    case 'selectOption':
      return { ...baseStep, type: 'selectOption', selector: 'select', value: 'option-value' }
    case 'check':
      return { ...baseStep, type: 'check', selector: 'input[type="checkbox"]' }
    case 'uncheck':
      return { ...baseStep, type: 'uncheck', selector: 'input[type="checkbox"]' }
    case 'waitForSelector':
      return { ...baseStep, type: 'waitForSelector', selector: 'element', state: 'visible' }
    case 'waitForTimeout':
      return { ...baseStep, type: 'waitForTimeout', duration: 1000 }
    case 'extractText':
      return { ...baseStep, type: 'extractText', selector: 'element', variableName: getDefaultVariableName('extractText') }
    case 'extractAttribute':
      return { ...baseStep, type: 'extractAttribute', selector: 'element', attribute: 'href', variableName: getDefaultVariableName('extractAttribute') }
    case 'takeScreenshot':
      return { ...baseStep, type: 'takeScreenshot', fullPage: false, variableName: getDefaultVariableName('takeScreenshot') }
    case 'conditionalClick':
      return { ...baseStep, type: 'conditionalClick', selector: 'button', condition: 'exists', clickIfTrue: true, button: 'left' }
    case 'fillPassword':
      return { ...baseStep, type: 'fillPassword', selector: 'input[type="password"]', credentialId: '' }
    case 'fill2FA':
      return { ...baseStep, type: 'fill2FA', selector: 'input', credentialId: '' }
    case 'downloadFile':
      return { ...baseStep, type: 'downloadFile', triggerSelector: 'a[href*=".pdf"]', filename: '', variableName: getDefaultVariableName('downloadFile'), saveToFile: false, forceDownload: false }
    case 'extractPdfValues':
      return { ...baseStep, type: 'extractPdfValues', base64Input: '', prompt: 'Extrahera namn, telefonnummer och email från dokumentet', variableName: 'extractedData' }
    case 'fortnoxCreateVoucher':
      return { ...baseStep, type: 'fortnoxCreateVoucher', description: 'Skapa verifikation i Fortnox med AI', inputVariable: '', aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed', voucherSeries: 'A', variableName: 'var-fortnox-voucher' }
    case 'fortnoxUploadFile':
      return { ...baseStep, type: 'fortnoxUploadFile', inputVariable: '', filename: '', description: 'Ladda upp fil till Fortnox Arkiv', variableName: 'var-fortnox-file' }
    case 'fortnoxAttachFileToVoucher':
      return { ...baseStep, type: 'fortnoxAttachFileToVoucher', fileIdVariable: '', voucherNumberVariable: '', voucherSeriesVariable: 'A', variableName: 'var-fortnox-attachment' }
    case 'fortnoxUploadAndCreateVoucher':
      return { ...baseStep, type: 'fortnoxUploadAndCreateVoucher', fileInputVariable: '', filename: '', fileDescription: 'Bifogad fil', voucherInputVariable: '', aiPrompt: 'Skapa en balanserad verifikation baserat på input-data enligt svensk bokföringssed', voucherDescription: 'Verifikation med bifogad fil', voucherSeries: 'A', variableName: 'var-fortnox-voucher-with-file' }
    default:
      throw new Error(`Unknown step type: ${stepType}`)
  }
}

export function getStepLabel(step: RpaStep): string {
  switch (step.type) {
    case 'navigate':
      return step.url || 'Navigate to URL'
    case 'goBack':
      return 'Go back'
    case 'goForward':
      return 'Go forward'
    case 'reload':
      return 'Reload page'
    case 'click':
      return step.selector || 'Click element'
    case 'fill':
      return `Fill: ${step.selector || 'element'}`
    case 'type':
      return `Type: ${step.text || 'text'}`
    case 'selectOption':
      return `Select: ${step.value || step.label || (step.index !== undefined ? `index ${step.index}` : 'option')}`
    case 'check':
      return `Check: ${step.selector || 'checkbox'}`
    case 'uncheck':
      return `Uncheck: ${step.selector || 'checkbox'}`
    case 'waitForSelector':
      return `Wait for: ${step.selector || 'element'}`
    case 'waitForTimeout':
      return `Wait ${step.duration || 1000}ms`
    case 'extractText':
      return `Extract: ${step.variableName || 'text'}`
    case 'takeScreenshot':
      return 'Take screenshot'
    case 'conditionalClick':
      const condStep = step as ConditionalClickStep
      const action = condStep.clickIfTrue ? 'Click if' : 'Click if not'
      return `${action} ${condStep.condition}: ${condStep.selector || 'element'}`
    case 'fillPassword':
      return `Fill password: ${step.selector || 'field'}`
    case 'fill2FA':
      return `Fill 2FA: ${step.selector || 'field'}`
    case 'downloadFile':
      const downloadStep = step as DownloadFileStep
      return downloadStep.triggerSelector
        ? `Download via: ${downloadStep.triggerSelector}`
        : 'Download file'
    case 'extractPdfValues':
      const extractStep = step as ExtractPdfValuesStep
      return `Extract PDF values: ${extractStep.prompt.substring(0, 50)}...`
    case 'fortnoxCreateVoucher':
      const fortnoxStep = step as FortnoxCreateVoucherStep
      return `Fortnox verifikation: ${fortnoxStep.description || 'Skapa verifikation'}`
    case 'fortnoxUploadFile':
      const uploadStep = step as FortnoxUploadFileStep
      return `Fortnox ladda upp fil: ${uploadStep.filename || uploadStep.inputVariable || 'fil'}`
    case 'fortnoxAttachFileToVoucher':
      const attachStep = step as FortnoxAttachFileToVoucherStep
      return `Fortnox bifoga fil: ${attachStep.fileIdVariable} → ${attachStep.voucherNumberVariable}`
    case 'fortnoxUploadAndCreateVoucher':
      const combinedStep = step as FortnoxUploadAndCreateVoucherStep
      return `Fortnox fil + verifikation: ${combinedStep.voucherDescription || 'Skapa verifikation med fil'}`
    default:
      return step.type
  }
}



// React Flow conversion functions
export function convertFlowToReactFlow(flow: RpaFlow): FlowData {
  const nodes: FlowNode[] = flow.steps.map((step, index) => {
    // Create better positioning for AI-generated flows
    // Use a slight horizontal offset to prevent overlapping
    const baseX = 100
    const baseY = 50
    const verticalSpacing = 120
    const horizontalOffset = (index % 2) * 30 // Small alternating offset

    return {
      id: step.id,
      type: 'rpaStep',
      position: {
        x: baseX + horizontalOffset,
        y: baseY + (index * verticalSpacing)
      },
      data: {
        step,
        label: getStepLabel(step)
      }
    }
  })

  const edges: FlowEdge[] = []
  for (let i = 0; i < flow.steps.length - 1; i++) {
    edges.push({
      id: `${flow.steps[i].id}-${flow.steps[i + 1].id}`,
      source: flow.steps[i].id,
      target: flow.steps[i + 1].id,
      type: 'smoothstep'
    })
  }

  return { nodes, edges }
}

export function convertReactFlowToFlow(flowData: FlowData, flowId: string, flowName: string, customerId: string = ''): RpaFlow {
  // Build a map of connections
  const connections = new Map<string, string>()
  flowData.edges.forEach(edge => {
    connections.set(edge.source, edge.target)
  })

  // Find the starting node (no incoming edges)
  const hasIncoming = new Set(flowData.edges.map(e => e.target))
  const startNodes = flowData.nodes.filter(node => !hasIncoming.has(node.id))

  if (startNodes.length === 0 && flowData.nodes.length > 0) {
    // If no clear start, use first node
    const steps = flowData.nodes.map(node => node.data.step)
    return {
      id: flowId,
      name: flowName,
      description: '',
      customerId,
      steps,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }

  // Build ordered steps by following connections
  const steps: RpaStep[] = []
  const visited = new Set<string>()

  function addStepsFromNode(nodeId: string) {
    if (visited.has(nodeId)) return
    visited.add(nodeId)

    const node = flowData.nodes.find(n => n.id === nodeId)
    if (node) {
      steps.push(node.data.step)
      const nextNodeId = connections.get(nodeId)
      if (nextNodeId) {
        addStepsFromNode(nextNodeId)
      }
    }
  }

  if (startNodes.length > 0) {
    addStepsFromNode(startNodes[0].id)
  }

  return {
    id: flowId,
    name: flowName,
    description: '',
    customerId,
    steps,
    createdAt: new Date(),
    updatedAt: new Date()
  }
}
