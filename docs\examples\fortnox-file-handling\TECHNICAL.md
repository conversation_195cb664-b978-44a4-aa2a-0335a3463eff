# Fortnox Filhantering - Teknisk Dokumentation

## Arkitektur

### Step Executors

Fortnox filhantering implementeras genom fyra step executors i `backend/src/runners/api/stepExecutors/`:

1. **`fortnoxUploadFile.ts`** - Hanterar filuppladdning till Fortnox Archive API
2. **`fortnoxAttachFileToVoucher.ts`** - Kopplar filer till verifikationer
3. **`fortnoxCreateVoucher.ts`** - Utökad med filkopplingsstöd
4. **`fortnoxUploadAndCreateVoucher.ts`** - Kombinerad operation

### API-endpoints

```typescript
// Filuppladdning
POST https://api.fortnox.se/3/archive
Content-Type: multipart/form-data
Authorization: Bearer {token}

// Filkoppling
POST https://api.fortnox.se/3/voucherfileconnections
Content-Type: application/json
{
  "VoucherFileConnection": {
    "FileId": "string",
    "VoucherNumber": number,
    "VoucherSeries": "string"
  }
}
```

## TypeScript Interfaces

### Step Definitions

```typescript
// Filuppladdning
interface FortnoxUploadFileStep extends RpaStepBase {
  type: 'fortnoxUploadFile';
  inputVariable: string;        // Base64 filinnehåll
  filename?: string;           // Anpassat filnamn
  description?: string;        // Filbeskrivning
  variableName?: string;       // Output variabel
}

// Filkoppling
interface FortnoxAttachFileToVoucherStep extends RpaStepBase {
  type: 'fortnoxAttachFileToVoucher';
  fileIdVariable: string;           // Fil-ID från uppladdning
  voucherNumberVariable: string;    // Verifikationsnummer
  voucherSeriesVariable?: string;   // Verifikationsserie
  variableName?: string;           // Output variabel
}

// Kombinerad operation
interface FortnoxUploadAndCreateVoucherStep extends RpaStepBase {
  type: 'fortnoxUploadAndCreateVoucher';
  // Fil-relaterade fält
  fileInputVariable: string;
  filename?: string;
  fileDescription?: string;
  // Verifikations-relaterade fält
  voucherInputVariable: string;
  aiPrompt: string;
  voucherDescription?: string;
  voucherSeries?: string;
  transactionDate?: string;
  variableName?: string;
}
```

### API Response Types

```typescript
// Fortnox Archive Response
interface FortnoxArchiveResponse {
  Archive: {
    Id: string;
    Name: string;
    Size: number;
    Path: string;
  };
}

// Voucher File Connection Response
interface FortnoxVoucherFileConnectionResponse {
  VoucherFileConnection: {
    '@url': string;
    FileId: string;
    VoucherNumber: number;
    VoucherSeries: string;
  };
}
```

## Implementeringsdetaljer

### Filuppladdning (fortnoxUploadFile)

```typescript
// Base64 → Buffer konvertering
let base64Data = base64Content;
if (typeof base64Data === 'string' && base64Data.includes(',')) {
  base64Data = base64Data.split(',')[1]; // Ta bort data URL prefix
}
const fileBuffer = Buffer.from(base64Data, 'base64');

// FormData för multipart upload
const formData = new FormData();
formData.append('file', fileBuffer, {
  filename: filename,
  contentType: 'application/octet-stream'
});

// API-anrop
const response = await axios.post(
  'https://api.fortnox.se/3/archive',
  formData,
  {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
      ...formData.getHeaders()
    }
  }
);
```

### Filkoppling (fortnoxAttachFileToVoucher)

```typescript
// Extrahera fil-ID från variabel
let fileId: string;
if (typeof fileData === 'string') {
  fileId = fileData;
} else if (fileData?.fileId) {
  fileId = fileData.fileId;
}

// Skapa koppling
const connection = {
  FileId: fileId,
  VoucherNumber: voucherNumber,
  VoucherSeries: voucherSeries
};

await axios.post(
  'https://api.fortnox.se/3/voucherfileconnections',
  { VoucherFileConnection: connection },
  { headers: { Authorization: `Bearer ${token}` } }
);
```

### Kombinerad Operation

Utför tre operationer sekventiellt:
1. Ladda upp fil till arkiv
2. Skapa verifikation med AI
3. Koppla fil till verifikation

```typescript
// 1. Filuppladdning
const uploadResponse = await uploadToArchive(fileBuffer, filename);
const fileId = uploadResponse.data.Archive.Id;

// 2. Verifikationsskapande
const voucherResponse = await createVoucherWithAI(inputData, aiPrompt);
const voucher = voucherResponse.data.Voucher;

// 3. Filkoppling
await attachFileToVoucher(fileId, voucher.VoucherNumber, voucher.VoucherSeries);
```

## Felhantering

### Vanliga API-fel

```typescript
// Fortnox API felhantering
try {
  const response = await axios.post(url, data, config);
} catch (apiError: any) {
  const errorDetails = apiError.response?.data || apiError.message;
  const statusCode = apiError.response?.status;
  
  onLog({
    level: 'error',
    message: `Fortnox API error (${statusCode}): ${JSON.stringify(errorDetails)}`,
    stepId: step.id
  });
  
  throw new Error(`Fortnox API error (${statusCode}): ${JSON.stringify(errorDetails)}`);
}
```

### Variabelhantering

Alla Fortnox step executors hanterar både direkta variabelnamn och `${variableName}` syntax:

```typescript
// Hantera både direkta variabelnamn och ${variableName} syntax
let actualVariableName = step.inputVariable;

// Kontrollera om inputVariable innehåller ${...} syntax och extrahera variabelnamnet
const variableMatch = step.inputVariable.match(/^\$\{([^}]+)\}$/);
if (variableMatch) {
  actualVariableName = variableMatch[1];
}

const variableValue = variables[actualVariableName];

if (!variableValue) {
  onLog({
    level: 'error',
    message: `Available variables: ${Object.keys(variables).join(', ')}`,
    stepId: step.id
  });
  throw new Error(`No content found in variable: ${actualVariableName}`);
}
```

### Validering

```typescript
// Base64 validering
if (!base64Data || base64Data.trim() === '') {
  throw new Error('Base64 input is empty or invalid');
}

// Buffer konvertering med felhantering
let fileBuffer: Buffer;
try {
  fileBuffer = Buffer.from(base64Data, 'base64');
} catch (bufferError) {
  throw new Error(`Invalid base64 data: ${bufferError.message}`);
}

// Verifikationsbalans
if (Math.abs(totalDebit - totalCredit) > 0.01) {
  throw new Error(`Voucher is not balanced. Debit: ${totalDebit}, Credit: ${totalCredit}`);
}
```

## Testning

### Enhetstester

```typescript
// Mock Fortnox API
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('fortnoxUploadFile', () => {
  it('should upload file successfully', async () => {
    // Mock API response
    mockedAxios.post.mockResolvedValue({
      data: {
        Archive: {
          Id: 'test-file-id',
          Name: 'test.pdf',
          Size: 1234,
          Path: '/archive/test.pdf'
        }
      }
    });

    // Test execution
    const result = await executeFortnoxUploadFile(step, context);
    
    expect(result.success).toBe(true);
    expect(result.variables['var-fortnox-file'].fileId).toBe('test-file-id');
  });
});
```

### Integration Tests

```typescript
// Test med riktiga API-anrop (kräver test-token)
describe('Fortnox Integration', () => {
  beforeAll(() => {
    process.env.FORTNOX_TEST_TOKEN = 'test-token';
  });

  it('should complete full file upload and voucher creation flow', async () => {
    // 1. Upload file
    const uploadResult = await executeFortnoxUploadFile(uploadStep, context);
    
    // 2. Create voucher with file attachment
    const voucherStep = {
      ...createVoucherStep,
      fileIds: [uploadResult.variables['var-fortnox-file'].fileId]
    };
    const voucherResult = await executeFortnoxCreateVoucher(voucherStep, context);
    
    expect(voucherResult.success).toBe(true);
    expect(voucherResult.variables['var-fortnox-voucher'].attachedFiles).toContain(
      uploadResult.variables['var-fortnox-file'].fileId
    );
  });
});
```

## Performance Considerations

### Filstorlek

```typescript
// Kontrollera filstorlek före uppladdning
const maxFileSize = 10 * 1024 * 1024; // 10MB
if (fileBuffer.length > maxFileSize) {
  throw new Error(`File too large: ${fileBuffer.length} bytes (max: ${maxFileSize})`);
}
```

### Timeout-hantering

```typescript
// Längre timeout för stora filer
const config = {
  timeout: fileBuffer.length > 1024 * 1024 ? 60000 : 30000, // 60s för stora filer
  maxContentLength: Infinity,
  maxBodyLength: Infinity
};
```

### Minneshantering

```typescript
// Frigör buffer efter användning
try {
  const result = await uploadFile(fileBuffer);
  return result;
} finally {
  // Buffer frigörs automatiskt av GC, men kan forceras
  if (global.gc) global.gc();
}
```

## Säkerhet

### Token-hantering

```typescript
// Hämta token säkert från customerService
const fortnoxTokens = await customerService.getCustomerTokensWithData(customerId);
const fortnoxToken = fortnoxTokens.find(token => 
  token.provider === 'Fortnox' && token.apiToken
);

if (!fortnoxToken?.apiToken) {
  throw new Error('No valid Fortnox token found for customer');
}
```

### Input-validering

```typescript
// Validera filformat
const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
const fileType = detectFileType(base64Data);
if (!allowedTypes.includes(fileType)) {
  throw new Error(`Unsupported file type: ${fileType}`);
}

// Sanitera filnamn
const sanitizedFilename = filename.replace(/[^a-zA-Z0-9.-]/g, '_');
```

## Monitoring och Logging

### Strukturerad Loggning

```typescript
onLog({
  level: 'info',
  message: `File uploaded successfully: ${filename} (ID: ${fileId})`,
  stepId: step.id,
  data: {
    fileId,
    filename,
    size: fileBuffer.length,
    customerId,
    operation: 'fortnox_upload_file'
  }
});
```

### Metrics

```typescript
// Spåra uppladdningstider
const startTime = Date.now();
const result = await uploadFile();
const duration = Date.now() - startTime;

onLog({
  level: 'info',
  message: `Upload completed in ${duration}ms`,
  stepId: step.id,
  data: { duration, fileSize: fileBuffer.length }
});
```
